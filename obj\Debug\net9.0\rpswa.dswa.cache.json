{"GlobalPropertiesHash": "OidH1Bsft7ZFomO+89jKFVawliuLdtnfCn5LvIWdAug=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["UKPnSpwunjkV6s+wBWe7HwvQDjDEhMgigclirta34A0=", "466lhpO3ui5lLOk2Yo5tx5LaFjFjo2p3pCXj2Sya30I=", "nMeSO+bWyGqGjf0nyz+53P1e8fXNx0o6BHhO6OX7SE4=", "Mjzki00FJ05JHUtRv9Ejc2oLqlMW4Ws5BAJhZJ6I3sc=", "EWS303ePBP5/zWboO+2X36EZAHdWC5VmCJOeT440uWc=", "pVExsriUNwRUtqOXSFdSRBoUzGkJE/zcidjJz8kQgOo=", "70AJLY6xWQxwLAqSsJDNdzj/cIIh9Los2CkK0wRbs00=", "yLND+sCrlWgP8TLY37n5+BlTaS19VGrn+EULhdtB0tE=", "JZLk3pblPBFj1z37Z631jAN4ORnvOuqnAvuvoB/fcZ8=", "QA832sNiNTgs6FvGPquS/lYq/6+qI3m6636W58kOSSs=", "2zCeLqVEffaloV5UPSQuemQ8KrzVZ8k6dQehKySK1B0=", "hfO4C2MuIwjfgDU8JludAVLmwRxIFhn0ebt8Ws8mAGM=", "JcFn1a1tNR6eZ4LxPxcK8fn8qLm7wq5vHZjfmPxxskE=", "hak1esTuQcORAAahRilvzdWBtKYxOD7EYTL9fWHlqoo=", "gMZz4UaOC+DqwRClIbkj8WmxaKPvt3PYpqo0GXKhumg=", "HrhYj5mhAav5xDk9Not6yAWdc5woy7Sgz5cFnipVZ5g=", "KpFGrkbs5y5I0C1ukGtdDGiM35/WIpwXSvkSPrjbgWU=", "xibJWvI7UVwFmTxxaf6fC9IlGZNabf/oxmRGFwwiANE=", "SCQGoW46Atz7goRjwYGcFb8xu7UzT6qHNbKONTfHqK4=", "+nKuaC0QDaC/h57xWg9h1Scu31FahptcAGJOF/aZuvU=", "+ifCGirbzvCIjgGf36A4aJNf6Vh1EmKzTeH4v6XRHzM=", "HJokXf7F2XHA4RYYtV1ccz22CI/J9mmg4lC2OVVmnx0=", "YpA49xq66ttgn1v1FCRxxHMxyefRHDvat9y6a2MM7Es=", "mg32ZxZbj0PnTxsbYiXa72WGT2d38wryR/sYsXt8WGg=", "PtvuU8rqB+aAtStpL6+uxy4oLNoSrzRuL+TUS1kFh1M=", "8I+CxTF9t/9ts9KcGjGQI7MFAfZZaum4M5+WfvE6Fxw=", "HCsCEGI0ts5OPKzjrtT52gIXGQQW0GBfDW4790Df0oE=", "CPLVNLAadidHo19zcvUv9MsZ0Rx9qvWX4M7GpAmSOT8=", "nKOWNxICsJUQh+jm8NBvsPdjcM2QRULWXhnZ4T4oIiI=", "brtEF1x3KQZkGc+DBFJwgV6CHzh6XAvcEvaC+oT6ZCQ=", "Gq/zuUJ9IXd732r6/d/iWnAKaLwp4my0Q7zxcicgbqk=", "gXwkRKbbjocR/HARsdjKZw37gVKx043y4cu+X4rLhd8=", "JcIWrGHcWuTi2mGN8hqmKF5vewf0vt1M0sO2c+NaSdE=", "iE1UTOoJbdLyupahdBXM973KPjJ88fmnHHMkeNnGBuE=", "qwmNewy62q8/IT24+ruuOoMGrU/QQ6LyaoJzUhUeGnU=", "Bh3orNTVGb5A67/yTfFDFVB14UtOT8dSli+xOgIhvGQ=", "x5nW1FNjUeGpQcf+T4FQFzxbFpF1gExjxxyTt1lxjvs=", "sYTmrwWJIe3Ur8xDYpMdddWYPkRkNcIgsqAkQReYO8g=", "vDu2ZCWqBYz3Zzn6AkJEU5vVXAGFgOWuXi2Xrx6118E=", "578eGqEpUsavhKfHrJEAiMe3VhWSDB6/ktg6LrKuZek=", "Pu8TYg1ddC08QiPD04d5y1QWci8wKyY5/LGyLnsVHl0=", "sOfRnENERx2rusqIv095unNsKi52MmVjBSFhqnzm+Ks=", "+nnJwMKTtzJg2XbVsMYq5wIqIIPmJmCGE71GR2hFflk=", "yXDrjvdTTg1hDq4gJTdMLCoPlpv0VDnBbcNXQ4LnsZ0=", "huU6XHL/DL/qbmIiqueaIwR+rvoWCdonNUEn2cDuxwA=", "0nuDRovG8w+8C9RiegKLUbjdwgGxnmV9NFzIoHm5wzM=", "UUEQXVg7/f+CVGiR4rS7S69m50abdVdaERAIJD9QJ3k=", "h2rAMwnxOsY7aGDUNlj6R7QV2gofSZpnAOiVb9+SBiA=", "WYI7dmrJfTrJTcx8p52icUCZAw0XJN5J/jvm2t1mI3w=", "Waxs3huMwhDH3nHSnFhzhSBwYSev2Yj5xsF83H4GOU8=", "2xE1j8KFFAPzmuB6txCzl5lAMsckPQxiSuF2aT8+fl4=", "tL120rA2q7ACteKMY6UxFsDadyeHfGwP4sRBsBvEheg=", "bbWk8gPAX0qAgESxFEvdPaqHXJ7LlqBg6eIvRjBS/Fw=", "/UOK06kVmm28d/x64ydqUCUlDLul0rxtzFNs4DpnEmE=", "OnpztVWRMUGOCq4FpJb89CnCCMzgn5n64biuQR5Me4Q=", "8bcRY0/n+K29mcdCCQabwoBcAtHUHwo+2u6wuAMppZA=", "5vzqyEDrQTjKRkDoguly8sQ9HCEEdsB0WBF8cD9lnVo=", "x6BlWHroAUAxvvpSCZq8dIca8BIdaSNFRLepZdly0Uc=", "v9wVyWiv0u/bYb/Qj6P5MJ3vHNrIG7U4tOMMGX1oTh4=", "TWd62veyIehTXb3al/ZPUlxJYb72rCgLRLVruS4+i3k=", "dygTawq6LvDgjc50CREYSbjeJdzI602Hsj2HN2eq0As=", "k4CtJ6YUilGtqmFdGUIrHvy+yCPk+KvwxjB+EUcLUtw=", "HUVR4LKMvYSgUHrEzghtbbzIhYl+SwiuFYJoiiEMTYM=", "PP6TH9UuzqFnLt8Trjv/QQLTz8fd9w9qcLKrAy34UdM=", "SU4T1qXkJ8OyU4GN1iyfjkG+Sp1aUcC66VPQIdTmPE8=", "rRkWcgMmJkH4ILt/av6blDHTuD18Dag7aVtUAQLkIJA=", "ryRaN33ZaoUDLmvS+3qlB61YRMjvsqorml8sPPtnw2c=", "YG4Jl2Pj0CmNdNIN+qBy7kF2fEgoqddPeAQM7SKSWsE=", "m+qBx49a01VEzILDmgcP2fVMC9FunfADRINEKO0AMew=", "gWtWJf0n0o24ZvF3CGxE8C5Jxxjgw7rWVMkFvQP0yd0=", "2v58AMgM1wzcO4bvIYh0VZWHEtGuCML61JfNdeF8bjo=", "NciiqSvfI5dyTh01iPY+AjwfAGun+yUJzLR0PNfxPks=", "gwH51m3lIQ8jpycCpPw1PkUvk6DsnvpOms6VmilsFgc="], "CachedAssets": {"UKPnSpwunjkV6s+wBWe7HwvQDjDEhMgigclirta34A0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\css\\site.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pn5zq41j80", "Integrity": "ZcGDultfae8/2mAp6AyVl/M5v2TwFGTvTgxfx9gaoCs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 12103, "LastWriteTime": "2025-08-04T11:48:27.571302+00:00"}, "466lhpO3ui5lLOk2Yo5tx5LaFjFjo2p3pCXj2Sya30I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\favicon.ico", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-08-04T09:12:57.0459528+00:00"}, "Mjzki00FJ05JHUtRv9Ejc2oLqlMW4Ws5BAJhZJ6I3sc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\js\\site.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-08-04T09:12:57.068709+00:00"}, "EWS303ePBP5/zWboO+2X36EZAHdWC5VmCJOeT440uWc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-08-04T09:12:56.9487136+00:00"}, "pVExsriUNwRUtqOXSFdSRBoUzGkJE/zcidjJz8kQgOo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-08-04T09:12:56.9502176+00:00"}, "70AJLY6xWQxwLAqSsJDNdzj/cIIh9Los2CkK0wRbs00=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-08-04T09:12:56.9502176+00:00"}, "yLND+sCrlWgP8TLY37n5+BlTaS19VGrn+EULhdtB0tE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-08-04T09:12:56.9502176+00:00"}, "JZLk3pblPBFj1z37Z631jAN4ORnvOuqnAvuvoB/fcZ8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-08-04T09:12:56.9517127+00:00"}, "QA832sNiNTgs6FvGPquS/lYq/6+qI3m6636W58kOSSs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-08-04T09:12:56.9528124+00:00"}, "2zCeLqVEffaloV5UPSQuemQ8KrzVZ8k6dQehKySK1B0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-08-04T09:12:56.9528124+00:00"}, "hfO4C2MuIwjfgDU8JludAVLmwRxIFhn0ebt8Ws8mAGM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-08-04T09:12:56.9528124+00:00"}, "JcFn1a1tNR6eZ4LxPxcK8fn8qLm7wq5vHZjfmPxxskE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-08-04T09:12:56.954007+00:00"}, "hak1esTuQcORAAahRilvzdWBtKYxOD7EYTL9fWHlqoo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-08-04T09:12:56.9550526+00:00"}, "gMZz4UaOC+DqwRClIbkj8WmxaKPvt3PYpqo0GXKhumg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-08-04T09:12:56.9550526+00:00"}, "HrhYj5mhAav5xDk9Not6yAWdc5woy7Sgz5cFnipVZ5g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-08-04T09:12:56.9550526+00:00"}, "KpFGrkbs5y5I0C1ukGtdDGiM35/WIpwXSvkSPrjbgWU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-08-04T09:12:56.9561756+00:00"}, "xibJWvI7UVwFmTxxaf6fC9IlGZNabf/oxmRGFwwiANE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-08-04T09:12:56.9561756+00:00"}, "SCQGoW46Atz7goRjwYGcFb8xu7UzT6qHNbKONTfHqK4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-08-04T09:12:56.95738+00:00"}, "+nKuaC0QDaC/h57xWg9h1Scu31FahptcAGJOF/aZuvU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-08-04T09:12:56.95738+00:00"}, "+ifCGirbzvCIjgGf36A4aJNf6Vh1EmKzTeH4v6XRHzM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-08-04T09:12:56.9585363+00:00"}, "HJokXf7F2XHA4RYYtV1ccz22CI/J9mmg4lC2OVVmnx0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-08-04T09:12:56.9602451+00:00"}, "YpA49xq66ttgn1v1FCRxxHMxyefRHDvat9y6a2MM7Es=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-08-04T09:12:56.9602451+00:00"}, "mg32ZxZbj0PnTxsbYiXa72WGT2d38wryR/sYsXt8WGg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-08-04T09:12:56.9612926+00:00"}, "PtvuU8rqB+aAtStpL6+uxy4oLNoSrzRuL+TUS1kFh1M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-08-04T09:12:56.9622923+00:00"}, "8I+CxTF9t/9ts9KcGjGQI7MFAfZZaum4M5+WfvE6Fxw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-08-04T09:12:56.9632971+00:00"}, "HCsCEGI0ts5OPKzjrtT52gIXGQQW0GBfDW4790Df0oE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-08-04T09:12:56.9632971+00:00"}, "CPLVNLAadidHo19zcvUv9MsZ0Rx9qvWX4M7GpAmSOT8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-08-04T09:12:56.9642971+00:00"}, "nKOWNxICsJUQh+jm8NBvsPdjcM2QRULWXhnZ4T4oIiI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-08-04T09:12:56.9652966+00:00"}, "brtEF1x3KQZkGc+DBFJwgV6CHzh6XAvcEvaC+oT6ZCQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-08-04T09:12:56.9664526+00:00"}, "Gq/zuUJ9IXd732r6/d/iWnAKaLwp4my0Q7zxcicgbqk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-08-04T09:12:56.9676634+00:00"}, "gXwkRKbbjocR/HARsdjKZw37gVKx043y4cu+X4rLhd8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-08-04T09:12:56.9686626+00:00"}, "JcIWrGHcWuTi2mGN8hqmKF5vewf0vt1M0sO2c+NaSdE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-08-04T09:12:56.970167+00:00"}, "iE1UTOoJbdLyupahdBXM973KPjJ88fmnHHMkeNnGBuE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-08-04T09:12:56.9716951+00:00"}, "qwmNewy62q8/IT24+ruuOoMGrU/QQ6LyaoJzUhUeGnU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-08-04T09:12:56.9737129+00:00"}, "Bh3orNTVGb5A67/yTfFDFVB14UtOT8dSli+xOgIhvGQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-08-04T09:12:56.9761103+00:00"}, "x5nW1FNjUeGpQcf+T4FQFzxbFpF1gExjxxyTt1lxjvs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-08-04T09:12:56.9783907+00:00"}, "sYTmrwWJIe3Ur8xDYpMdddWYPkRkNcIgsqAkQReYO8g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-08-04T09:12:56.9802768+00:00"}, "vDu2ZCWqBYz3Zzn6AkJEU5vVXAGFgOWuXi2Xrx6118E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-08-04T09:12:56.981286+00:00"}, "578eGqEpUsavhKfHrJEAiMe3VhWSDB6/ktg6LrKuZek=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-08-04T09:12:56.982284+00:00"}, "Pu8TYg1ddC08QiPD04d5y1QWci8wKyY5/LGyLnsVHl0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-08-04T09:12:56.9832826+00:00"}, "sOfRnENERx2rusqIv095unNsKi52MmVjBSFhqnzm+Ks=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-08-04T09:12:56.9852828+00:00"}, "+nnJwMKTtzJg2XbVsMYq5wIqIIPmJmCGE71GR2hFflk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-08-04T09:12:56.9852828+00:00"}, "yXDrjvdTTg1hDq4gJTdMLCoPlpv0VDnBbcNXQ4LnsZ0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-08-04T09:12:56.9862827+00:00"}, "huU6XHL/DL/qbmIiqueaIwR+rvoWCdonNUEn2cDuxwA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-08-04T09:12:56.9877901+00:00"}, "0nuDRovG8w+8C9RiegKLUbjdwgGxnmV9NFzIoHm5wzM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-08-04T09:12:56.9886385+00:00"}, "UUEQXVg7/f+CVGiR4rS7S69m50abdVdaERAIJD9QJ3k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-08-04T09:12:56.9886385+00:00"}, "h2rAMwnxOsY7aGDUNlj6R7QV2gofSZpnAOiVb9+SBiA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-08-04T09:12:56.9901476+00:00"}, "WYI7dmrJfTrJTcx8p52icUCZAw0XJN5J/jvm2t1mI3w=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-08-04T09:12:56.970167+00:00"}, "Waxs3huMwhDH3nHSnFhzhSBwYSev2Yj5xsF83H4GOU8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-08-04T09:12:57.0486876+00:00"}, "2xE1j8KFFAPzmuB6txCzl5lAMsckPQxiSuF2aT8+fl4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-08-04T09:12:57.051198+00:00"}, "tL120rA2q7ACteKMY6UxFsDadyeHfGwP4sRBsBvEheg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-08-04T09:12:56.9761103+00:00"}, "bbWk8gPAX0qAgESxFEvdPaqHXJ7LlqBg6eIvRjBS/Fw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-08-04T09:12:57.0399403+00:00"}, "/UOK06kVmm28d/x64ydqUCUlDLul0rxtzFNs4DpnEmE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-08-04T09:12:57.0459528+00:00"}, "OnpztVWRMUGOCq4FpJb89CnCCMzgn5n64biuQR5Me4Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-08-04T09:12:57.0469552+00:00"}, "8bcRY0/n+K29mcdCCQabwoBcAtHUHwo+2u6wuAMppZA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-08-04T09:12:57.0469552+00:00"}, "5vzqyEDrQTjKRkDoguly8sQ9HCEEdsB0WBF8cD9lnVo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-08-04T09:12:56.9737129+00:00"}, "x6BlWHroAUAxvvpSCZq8dIca8BIdaSNFRLepZdly0Uc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-08-04T09:12:56.9921548+00:00"}, "v9wVyWiv0u/bYb/Qj6P5MJ3vHNrIG7U4tOMMGX1oTh4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-08-04T09:12:56.993155+00:00"}, "TWd62veyIehTXb3al/ZPUlxJYb72rCgLRLVruS4+i3k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-08-04T09:12:56.9973022+00:00"}, "dygTawq6LvDgjc50CREYSbjeJdzI602Hsj2HN2eq0As=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-08-04T09:12:56.9983104+00:00"}, "k4CtJ6YUilGtqmFdGUIrHvy+yCPk+KvwxjB+EUcLUtw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-08-04T09:12:57.0000192+00:00"}, "HUVR4LKMvYSgUHrEzghtbbzIhYl+SwiuFYJoiiEMTYM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-08-04T09:12:57.0065007+00:00"}, "PP6TH9UuzqFnLt8Trjv/QQLTz8fd9w9qcLKrAy34UdM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-08-04T09:12:56.9727112+00:00"}, "nMeSO+bWyGqGjf0nyz+53P1e8fXNx0o6BHhO6OX7SE4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\Images\\Char.png", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\", "BasePath": "_content/HealthTrack", "RelativePath": "Images/Char#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "to87n5mv2v", "Integrity": "Qw3l/HFTTIXXhbEfZ+YgTnRFEuA5okbYrj3Gvn9t4ZA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Images\\Char.png", "FileLength": 2348745, "LastWriteTime": "2025-08-04T10:39:22.5761445+00:00"}}, "CachedCopyCandidates": {}}
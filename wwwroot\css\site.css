/* Root Variables */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --light-color: #f8fafc;
  --dark-color: #1e293b;
  --border-radius: 12px;
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --transition: all 0.3s ease;
}

/* Base Styles */
html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Cairo', sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
  background-color: #ffffff;
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--secondary-color);
  max-width: 600px;
  margin: 0 auto;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 600;
  padding: 12px 24px;
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.btn-lg {
  padding: 16px 32px;
  font-size: 1.1rem;
}

/* Navigation */
.navbar {
  padding: 1rem 0;
  transition: var(--transition);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color) !important;
}

.brand-text {
  color: var(--primary-color);
}

.nav-link {
  font-weight: 500;
  color: var(--dark-color) !important;
  padding: 8px 16px !important;
  border-radius: 8px;
  transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
  color: var(--primary-color) !important;
  background-color: rgba(37, 99, 235, 0.1);
}

/* Hero Section */
.hero-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23e2e8f0" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: var(--secondary-color);
  line-height: 1.6;
}

.min-vh-75 {
  min-height: 75vh;
}

/* Image Placeholder */
.image-placeholder {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--border-radius);
  padding: 60px 40px;
  text-align: center;
  color: white;
  box-shadow: var(--box-shadow);
}

.image-placeholder i {
  font-size: 6rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.image-placeholder p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.8;
}

/* Features Section */
.features-section {
  padding: 80px 0;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  font-size: 2rem;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 16px;
}

.feature-description {
  color: var(--secondary-color);
  line-height: 1.6;
  margin: 0;
}

/* Testimonials Section */
.testimonials-section {
  padding: 80px 0;
  background-color: var(--light-color);
}

.testimonial-card {
  background: white;
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  height: 100%;
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.testimonial-content {
  margin-bottom: 24px;
}

.stars {
  font-size: 1.1rem;
}

.testimonial-text {
  font-style: italic;
  color: var(--secondary-color);
  line-height: 1.6;
  margin: 0;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.author-name {
  font-weight: 600;
  color: var(--dark-color);
  margin: 0;
}

.author-location {
  color: var(--secondary-color);
  font-size: 0.9rem;
  margin: 0;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.cta-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Footer */
.footer {
  margin-top: auto;
}

.social-links a {
  transition: var(--transition);
}

.social-links a:hover {
  color: var(--primary-color) !important;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero-section {
    padding: 60px 0;
  }

  .features-section,
  .testimonials-section,
  .cta-section {
    padding: 60px 0;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 12px;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .navbar-brand {
    font-size: 1.3rem;
  }
}

/* Utility Classes */
.text-primary {
  color: var(--primary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Focus States */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Logo Placeholder */
.logo-placeholder {
  transition: var(--transition);
}

.navbar-brand:hover .logo-placeholder {
  transform: scale(1.1);
}
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-rvqxkrtq0i] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-rvqxkrtq0i] {
  color: #0077cc;
}

.btn-primary[b-rvqxkrtq0i] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-rvqxkrtq0i], .nav-pills .show > .nav-link[b-rvqxkrtq0i] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-rvqxkrtq0i] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-rvqxkrtq0i] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-rvqxkrtq0i] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-rvqxkrtq0i] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-rvqxkrtq0i] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

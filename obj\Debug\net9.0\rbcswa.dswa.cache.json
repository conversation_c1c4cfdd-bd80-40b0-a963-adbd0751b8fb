{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["mpj/RAKn7g0UdmPbB8KJ7R9ZXtGiqWCRnzmxzhQVMog=", "TtnKM87XxKx+Og20IWw1BB3VLSKbBjwNqf8ePj/Zx74=", "8K1HOsW3rXKQ0g+RwAxkCffmTHHxNc14UjpaXmF+Zto=", "34GljogPlDxwTitIP20qs56SceQYPAZWFKaLFEH6Om0=", "Nr8a+IS5y1ktTQtqLkhkkCYf/rfl40u7HA0elFa6KmA=", "EwCWKJYLYZsME6Pv6sfazL1Qnpk8toTw3RkiOvphmxY=", "S9CLcgLxJkcBGHJEBoxWEVW94+bW2VXHECymhfE3ncc=", "sM+tjAr4AYtjBjUHZ+mCDrmCUrF8jhEWvPScVo+SMjg=", "MvKwudmLfdr5I0BZD89PHEri+rIOS9E97eKRn37Pq60=", "S6AXoYmu0r5yd8txCfh3L0f9nMBcVg7XkwgRlKT96WA=", "kL7lpAstinsRiBYhleC2M+cZ9a5o2E9FNWRLXKq7mlM=", "fxTahgDiPxh8363KM9gVHr8cjHYW4gob608rsMiLn/4=", "rJxH/P+XQuKrE/t8C3Ng0pVuDvj2qPl7gIiBguGnyqQ=", "QNybOSTpHtpJUYfk40eqmPEP5Di5/wu48e0dplj/3L0=", "/ZL3h/wD0KnoDuehpDJ8jAXFVBfiXotx0zgeaN68gao=", "Ey1ze5E28CEN29CpO863dOetzBWndBVwhamiiyHSgSM=", "wQ5Ps7aw1idoXv0JkhVLXBrJ9Ul8GjWCyznfF8vMEKY=", "JT7CkRPKqJaJomaIh3MkU09sr/7P9v6KOgKgUZ7Fw+I=", "4HuZqQhKqMkTKqAS8HwMJMSdck0YBcdiD4riV0PDXpA=", "qiXd22VB3wYGi+y0ocXEBI+xcuKOVqa4ZzOZFgeMIVA=", "XppW7llvbMiHvJi3Z3BAI1kt6ut8k59j63lOWbwYZJg=", "nVmzZ2f0FGPkOLJO6TWMRxJIuORC2kzOqAAusVxxA+Q=", "G0BzUs3agiMm/SqKOoNdUy265HDXBWwwkWvu2+QVms0=", "GaR38I+1u6aAquSiLXCSzyHjzJzvo7xk9QR/PzNjZFs=", "dV+EJQgAHK2WI9QZLOaJorG+DxXPw0v9wS8JaQoEHrg=", "ozjETk6Z3UdYMM2Pe5xdaOcSveubsmtjfdaLvRtfsk8=", "yDN7JB8okeFBfs1oxpCJo8JNuiZqsCxuAqpmo2bLQQY=", "z/cCoZg8ZpSMqhdCyMiGIfuIqj9SmL0RITBe7tk+i1Y=", "glCYNt8QDwS2i9BuzNlSAG9yC/IUgKS3iYLRuAsSzw4=", "H6y2AcqmYESuGqEcWOk1W80oddrvJl6vXbvV298vvnk=", "N+W0E6+cEu41+h4NGKqiA5j0Ow6DLlYD2Ea6Z+rlPn0=", "qfAfUvE2Nu2Xypb5ewmR0ULY41hZrKGvfaVC++a4sJE=", "4vslu7CdN9pwvJdsQvO0/OLot8P2TG0SPEYQDC1Mg98=", "/J1LTu7NM/xDRiCGCYfShNg6l4ZXO0mzosmnBthMXdo=", "yPeeOfO13FG6LKmGVKz31P2d2+bk9CCxFjcfcTpk2/A=", "weA1QVe7UnJxz0NSa2LjXFpr9mP8jGjx4r8KXho6lws=", "YjWtyjS65RCoDfT2CFiAEOe7DLxgNCgn6C9uNfVktVY=", "2ePb4uymm8Anchrz+TCCquL+mZOk35SqLrf3hVtoZtQ=", "4H+kMeB4gSad0LvLVvlS8f5XdrQ2cO/XKJ8HXivZPpo=", "khrYsELL7Gi7iSH6PRzaJd68T5g6pbKQNaZmVj++e+g=", "9NjG+0xWdpjJGOho1hYst/gQbzCBnbDOTEL+CzlzCtU=", "w94t1GvzuXz+OGYnZ/WPYGRnhM2Hwtehq/+XE8CChCI=", "f1cHUyirJ86y4eo2DnhWAR5pY+V5gizWU6IFGKI6794=", "YJMejpw8RavCHoI1pUnxW7OwWcGuYU8fEfTfXqxcbfM=", "alx+jssT9LXHoMdAEZ/URaFXgxXiKL3xPyuK1tgAdOY=", "VsOMfpwJKAj+UP8fDqPJZuEZn1Q9nzcdeX5pLACJAao=", "HBO3f27Og3EpVfUA0iDABUPGKiVWuBhaDRFJGED57KQ=", "GUSigMypl2t4FkByJTobPaoaE6eepkqMhN/uvI8Sl6s=", "qD+OAYsmVlPCu5XUI43nfDr9dTHpEcJ2XfiNFn0MUYg=", "JXMhML0/0CjO/R+Ci4fv3EuqwGMW/WGDE0/Y7EZKzO4=", "uwTtwvG+yGmOCedp6+BBxT2CITv5M2ZBCr4pddvnGPU=", "7e3/3kZo/9pOoIsOfl0pNRYzp2FYJqbJwL3tqrf6pRc=", "ojZsKH2X/xBs2y8UWmojuodZ+kl+VWyeOpnNhFcCt3U=", "EGP6paps1z7eqYJkC22O8985cpk7eRSfHASrAZSee18=", "WLgJ2xW75WpkkoD7DzX52JVI4slEn/FmzuPKZl/Fng4=", "RsCf3xHDJrdEOAg9kse2JxAtFPwQiQ+16MJt3U6rqHs=", "2lMP+vpkJqTHI0OaXmUWQrbi5vELec6RnmcG2v3suuc=", "fwp5R1fOFEzjmFuXLiqp4qztUUwPcMKLIfOFubPnsvE=", "tDpIA3HvRE7cANrnBMUj4wc1O3AUycxXjOUnE+8lqik=", "Hun8ACH026r0pfbyAVn9GqS8kVWy8z/Nu0CribOjQDw=", "1/H5atHeqkV6dWYWBheBEirco0RHKAAmY8egbJ9KLgI=", "N00LeWjEigSv6cnxFiWBmlwAPwSt0xncimXErMbo6ak=", "PzmgQ8NNVl2v9WX2h1atzStYZ/Gx0KwoNLMQ8seR3+M=", "gr6UwCZJWtRR2OPIIIx4fv05juO6rlnP3Pe8FBkwU3g="], "CachedAssets": {"N00LeWjEigSv6cnxFiWBmlwAPwSt0xncimXErMbo6ak=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\q3i4l3ugj3-pn5zq41j80.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "css/site#[.{fingerprint=pn5zq41j80}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oul6gfmjrs", "Integrity": "4H9shfNAzNPcG5Jif684llwBLi505dJovHg72irYyXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\css\\site.css", "FileLength": 2751, "LastWriteTime": "2025-08-04T11:49:24.3424636+00:00"}, "mpj/RAKn7g0UdmPbB8KJ7R9ZXtGiqWCRnzmxzhQVMog=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rvghe3p6z1-61n19gt1b8.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "TtnKM87XxKx+Og20IWw1BB3VLSKbBjwNqf8ePj/Zx74=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rqnvvatrvf-xtxxf3hu2r.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "8K1HOsW3rXKQ0g+RwAxkCffmTHHxNc14UjpaXmF+Zto=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\4ipyayqy8h-bqjiyaj88i.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-08-04T09:31:54.269226+00:00"}, "34GljogPlDxwTitIP20qs56SceQYPAZWFKaLFEH6Om0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\4qf12f94f2-c2jlpeoesf.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-08-04T09:31:54.2761428+00:00"}, "Nr8a+IS5y1ktTQtqLkhkkCYf/rfl40u7HA0elFa6KmA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qqylkh41a8-erw9l3u2r3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-08-04T09:31:54.2525247+00:00"}, "EwCWKJYLYZsME6Pv6sfazL1Qnpk8toTw3RkiOvphmxY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\woq4dz0xua-aexeepp0ev.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "S9CLcgLxJkcBGHJEBoxWEVW94+bW2VXHECymhfE3ncc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\0y9i614yx0-d7shbmvgxk.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "sM+tjAr4AYtjBjUHZ+mCDrmCUrF8jhEWvPScVo+SMjg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ryyztizykj-ausgxo2sd3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-08-04T09:31:54.2682257+00:00"}, "MvKwudmLfdr5I0BZD89PHEri+rIOS9E97eKRn37Pq60=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\c0saxi0s91-k8d9w2qqmf.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "S6AXoYmu0r5yd8txCfh3L0f9nMBcVg7XkwgRlKT96WA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\1v3s5l7wgh-cosvhxvwiu.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-08-04T09:31:54.2642265+00:00"}, "kL7lpAstinsRiBYhleC2M+cZ9a5o2E9FNWRLXKq7mlM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7rrhbhrzll-ub07r2b239.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-08-04T09:31:54.2536127+00:00"}, "fxTahgDiPxh8363KM9gVHr8cjHYW4gob608rsMiLn/4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\6xtl83wvsb-fvhpjtyr6v.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-08-04T09:31:54.271226+00:00"}, "rJxH/P+XQuKrE/t8C3Ng0pVuDvj2qPl7gIiBguGnyqQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\gnwqkf1i4n-b7pk76d08c.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-08-04T09:31:54.2632084+00:00"}, "QNybOSTpHtpJUYfk40eqmPEP5Di5/wu48e0dplj/3L0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\v6viktehjb-fsbi9cje9m.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-08-04T09:31:54.269226+00:00"}, "/ZL3h/wD0KnoDuehpDJ8jAXFVBfiXotx0zgeaN68gao=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\55wl1xc63l-rzd6atqjts.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-08-04T09:31:54.2612096+00:00"}, "Ey1ze5E28CEN29CpO863dOetzBWndBVwhamiiyHSgSM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\mn48rd8flt-ee0r1s7dh0.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-08-04T09:31:54.2737061+00:00"}, "wQ5Ps7aw1idoXv0JkhVLXBrJ9Ul8GjWCyznfF8vMEKY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\0fzdp0mmbq-dxx9fxp4il.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-08-04T09:31:54.2612096+00:00"}, "JT7CkRPKqJaJomaIh3MkU09sr/7P9v6KOgKgUZ7Fw+I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\pq85vvh46t-jd9uben2k1.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "4HuZqQhKqMkTKqAS8HwMJMSdck0YBcdiD4riV0PDXpA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\wm9wbypqec-khv3u5hwcm.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-08-04T09:31:54.2702261+00:00"}, "qiXd22VB3wYGi+y0ocXEBI+xcuKOVqa4ZzOZFgeMIVA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\sellbaqaxa-r4e9w2rdcm.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-08-04T09:31:54.2602083+00:00"}, "XppW7llvbMiHvJi3Z3BAI1kt6ut8k59j63lOWbwYZJg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ix181bhp4y-lcd1t2u6c8.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-08-04T09:31:54.2612096+00:00"}, "nVmzZ2f0FGPkOLJO6TWMRxJIuORC2kzOqAAusVxxA+Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qdpaonnvru-c2oey78nd0.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-08-04T09:31:54.2722244+00:00"}, "G0BzUs3agiMm/SqKOoNdUy265HDXBWwwkWvu2+QVms0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\l8tofvflbn-tdbxkamptv.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-08-04T09:31:54.2682257+00:00"}, "GaR38I+1u6aAquSiLXCSzyHjzJzvo7xk9QR/PzNjZFs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\6e96ofz0xa-j5mq2jizvt.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-08-04T09:31:54.2789103+00:00"}, "dV+EJQgAHK2WI9QZLOaJorG+DxXPw0v9wS8JaQoEHrg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\qtj6vtw0nd-06098lyss8.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-08-04T09:31:54.2642265+00:00"}, "ozjETk6Z3UdYMM2Pe5xdaOcSveubsmtjfdaLvRtfsk8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\dbcclxakkw-nvvlpmu67g.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "yDN7JB8okeFBfs1oxpCJo8JNuiZqsCxuAqpmo2bLQQY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\nhrch8h37i-s35ty4nyc5.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-08-04T09:31:54.2799081+00:00"}, "z/cCoZg8ZpSMqhdCyMiGIfuIqj9SmL0RITBe7tk+i1Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ilinj6i829-pj5nd1wqec.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-08-04T09:31:54.2809103+00:00"}, "glCYNt8QDwS2i9BuzNlSAG9yC/IUgKS3iYLRuAsSzw4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7cm1wfiys2-46ein0sx1k.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-08-04T09:31:54.2839109+00:00"}, "H6y2AcqmYESuGqEcWOk1W80oddrvJl6vXbvV298vvnk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\rmu5iagomh-v0zj4ognzu.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-08-04T09:31:54.271226+00:00"}, "N+W0E6+cEu41+h4NGKqiA5j0Ow6DLlYD2Ea6Z+rlPn0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\ovuk4oicfx-37tfw0ft22.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-08-04T09:31:54.269226+00:00"}, "qfAfUvE2Nu2Xypb5ewmR0ULY41hZrKGvfaVC++a4sJE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\tjgqr9epjp-hrwsygsryq.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "4vslu7CdN9pwvJdsQvO0/OLot8P2TG0SPEYQDC1Mg98=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\cfmww4yjcd-pk9g2wxc8p.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-08-04T09:31:54.269226+00:00"}, "/J1LTu7NM/xDRiCGCYfShNg6l4ZXO0mzosmnBthMXdo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\c8ehi8me58-ft3s53vfgj.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-08-04T09:31:54.2839109+00:00"}, "yPeeOfO13FG6LKmGVKz31P2d2+bk9CCxFjcfcTpk2/A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\61wn5k8mma-6cfz1n2cew.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-08-04T09:31:54.2722244+00:00"}, "weA1QVe7UnJxz0NSa2LjXFpr9mP8jGjx4r8KXho6lws=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\3w18qcgqbv-6pdc2jztkx.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-08-04T09:31:54.2855818+00:00"}, "YjWtyjS65RCoDfT2CFiAEOe7DLxgNCgn6C9uNfVktVY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\i9vqlloia8-493y06b0oq.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-08-04T09:31:54.2737061+00:00"}, "2ePb4uymm8Anchrz+TCCquL+mZOk35SqLrf3hVtoZtQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\xyjekh6151-iovd86k7lj.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-08-04T09:31:54.2839109+00:00"}, "4H+kMeB4gSad0LvLVvlS8f5XdrQ2cO/XKJ8HXivZPpo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\gs6qiabfux-vr1egmr9el.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-08-04T09:31:54.2722244+00:00"}, "khrYsELL7Gi7iSH6PRzaJd68T5g6pbKQNaZmVj++e+g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\al4fw0ow1k-kbrnm935zg.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-08-04T09:31:54.269226+00:00"}, "9NjG+0xWdpjJGOho1hYst/gQbzCBnbDOTEL+CzlzCtU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\1he1gxmvb3-jj8uyg4cgr.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-08-04T09:31:54.2682257+00:00"}, "w94t1GvzuXz+OGYnZ/WPYGRnhM2Hwtehq/+XE8CChCI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\w6yic8hrgu-y7v9cxd14o.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-08-04T09:31:54.2747056+00:00"}, "f1cHUyirJ86y4eo2DnhWAR5pY+V5gizWU6IFGKI6794=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\atf70902cn-notf2xhcfb.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-08-04T09:31:54.2809103+00:00"}, "YJMejpw8RavCHoI1pUnxW7OwWcGuYU8fEfTfXqxcbfM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\alqhmjq9pq-h1s4sie4z3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "alx+jssT9LXHoMdAEZ/URaFXgxXiKL3xPyuK1tgAdOY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\mx05ib7p15-63fj8s7r0e.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "VsOMfpwJKAj+UP8fDqPJZuEZn1Q9nzcdeX5pLACJAao=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\tgopaorcaz-0j3bgjxly4.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-08-04T09:31:54.2809103+00:00"}, "HBO3f27Og3EpVfUA0iDABUPGKiVWuBhaDRFJGED57KQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\6omr1nulpi-47otxtyo56.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-08-04T09:31:54.2722244+00:00"}, "GUSigMypl2t4FkByJTobPaoaE6eepkqMhN/uvI8Sl6s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\q34pds2tf9-4v8eqarkd7.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "qD+OAYsmVlPCu5XUI43nfDr9dTHpEcJ2XfiNFn0MUYg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\x5sya4yjma-356vix0kms.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-08-04T09:31:54.2722244+00:00"}, "JXMhML0/0CjO/R+Ci4fv3EuqwGMW/WGDE0/Y7EZKzO4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\e6ha2kb8yt-83jwlth58m.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-08-04T09:31:54.2642265+00:00"}, "uwTtwvG+yGmOCedp6+BBxT2CITv5M2ZBCr4pddvnGPU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\n2kdrs17qm-mrlpezrjn3.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-08-04T09:31:54.2602083+00:00"}, "7e3/3kZo/9pOoIsOfl0pNRYzp2FYJqbJwL3tqrf6pRc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\12hqi648ev-lzl9nlhx6b.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-08-04T09:31:54.2652255+00:00"}, "ojZsKH2X/xBs2y8UWmojuodZ+kl+VWyeOpnNhFcCt3U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\7qorf53x51-ag7o75518u.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-08-04T09:31:54.2702261+00:00"}, "EGP6paps1z7eqYJkC22O8985cpk7eRSfHASrAZSee18=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\095chq0xao-x0q3zqp4vz.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-08-04T09:31:54.2682257+00:00"}, "WLgJ2xW75WpkkoD7DzX52JVI4slEn/FmzuPKZl/Fng4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\awxvykvflr-0i3buxo5is.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-08-04T09:31:54.2722244+00:00"}, "RsCf3xHDJrdEOAg9kse2JxAtFPwQiQ+16MJt3U6rqHs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\9689a7o82a-o1o13a6vjx.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-08-04T09:31:54.2682257+00:00"}, "2lMP+vpkJqTHI0OaXmUWQrbi5vELec6RnmcG2v3suuc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\jmdhlcauwa-ttgo8qnofa.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "fwp5R1fOFEzjmFuXLiqp4qztUUwPcMKLIfOFubPnsvE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\b4j78s1ol2-2z0ns9nrw6.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-08-04T09:31:54.2829099+00:00"}, "tDpIA3HvRE7cANrnBMUj4wc1O3AUycxXjOUnE+8lqik=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\d7mqztheys-muycvpuwrr.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-08-04T09:31:54.2747056+00:00"}, "Hun8ACH026r0pfbyAVn9GqS8kVWy8z/Nu0CribOjQDw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\9yd6thb4l9-87fc7y1x7t.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-08-04T09:31:54.2702261+00:00"}, "1/H5atHeqkV6dWYWBheBEirco0RHKAAmY8egbJ9KLgI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\c6ptl8ucl9-mlv21k5csn.gz", "SourceId": "HealthTrack", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-08-04T09:31:54.2632084+00:00"}, "PzmgQ8NNVl2v9WX2h1atzStYZ/Gx0KwoNLMQ8seR3+M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\hh6rz1azrz-6g5oj6kvn9.gz", "SourceId": "HealthTrack", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "HealthTrack#[.{fingerprint=6g5oj6kvn9}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\bundle\\HealthTrack.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1zewzlpo2", "Integrity": "GTs8kpYsYzKZ1J+UTHZDGI308djNdwNcmWzg92zdR6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\bundle\\HealthTrack.styles.css", "FileLength": 542, "LastWriteTime": "2025-08-04T09:31:54.2766486+00:00"}, "gr6UwCZJWtRR2OPIIIx4fv05juO6rlnP3Pe8FBkwU3g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\8m9gcaymu7-6g5oj6kvn9.gz", "SourceId": "HealthTrack", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/HealthTrack", "RelativePath": "HealthTrack#[.{fingerprint=6g5oj6kvn9}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\HealthTrack.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1zewzlpo2", "Integrity": "GTs8kpYsYzKZ1J+UTHZDGI308djNdwNcmWzg92zdR6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\HealthTrack\\HealthTrack\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\HealthTrack.bundle.scp.css", "FileLength": 542, "LastWriteTime": "2025-08-04T09:31:54.2777429+00:00"}}, "CachedCopyCandidates": {}}